import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:intl/intl.dart';
import '../../../../domain/models/top_performers.dart';
import '../../../cubit/top_performers/top_performers_cubit.dart';
import '/src/core/enum/user_role.dart';

import '../../../../core/config/app_strings.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/config/constants.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../domain/models/user.dart';
import '../../../cubit/user/user_cubit.dart';
import 'sales_by_brokers_card.dart';

class PickerPosition {
  final double left;
  final double top;

  const PickerPosition({required this.left, required this.top});
}

class MonthYearPicker extends StatefulWidget {
  final DateTime initialDate;
  final ValueChanged<DateTime> onDateSelected;
  final VoidCallback onCancel;
  final GlobalKey anchorKey;

  const MonthYearPicker({
    Key? key,
    required this.initialDate,
    required this.onDateSelected,
    required this.onCancel,
    required this.anchorKey, 
  }) : super(key: key);

  @override
  State<MonthYearPicker> createState() => _MonthYearPickerState();
}

class _MonthYearPickerState extends State<MonthYearPicker> {
  late int selectedYear;
  late int selectedMonth;
  bool showingYears = false;
  int yearPageIndex = 0;

  final DateTime today = DateTime.now();
  final DateTime firstDate = DateTime(2015, 1, 1);
  late final DateTime lastDate = DateTime(today.year, 12, 31);

  @override
  void initState() {
    super.initState();
    selectedYear = widget.initialDate.year.clamp(firstDate.year, today.year);
    selectedMonth = widget.initialDate.month;
    if (selectedYear == today.year && selectedMonth > today.month) {
      selectedMonth = today.month;
    }
    final yearIndex = selectedYear - firstDate.year;
    yearPageIndex = yearIndex ~/ 12;
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double pickerWidth = _calculatePickerWidth(screenWidth);
    return Theme(
      data: Theme.of(context).copyWith(
        colorScheme: ColorScheme.light(
          primary: Colors.blue,
          onPrimary: Colors.white,
          surface: Colors.white,
          onSurface: Colors.black,
        ),
      ),
      child: Material(
        elevation: 4.0,
        borderRadius: BorderRadius.circular(8.0),
        color: Colors.white,
        child: SizedBox(
          width: pickerWidth,
          height: 350,
          child: Column(
            children: [
              _buildHeader(),
              const SizedBox(height: 12),
              Expanded(
                child: showingYears ? _buildYearGrid() : _buildMonthGrid(),
              ),
              const SizedBox(height: 8),
              _buildActionButtons(),
              const SizedBox(height: 12),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8.0),
          topRight: Radius.circular(8.0),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () {
              setState(() {
                if (showingYears) {
                  if (yearPageIndex > 0) {
                    yearPageIndex--;
                  }
                } else {
                  selectedYear = (selectedYear - 1)
                      .clamp(firstDate.year, lastDate.year)
                      .toInt();
                }
              });
            },
            icon: Icon(Icons.chevron_left, color: Colors.black),
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                showingYears = !showingYears;
                if (showingYears) {
                  final yearIndex = selectedYear - firstDate.year;
                  yearPageIndex = yearIndex ~/ 12;
                }
              });
            },
            child: Text(
              showingYears ? _getYearPageRangeText() : selectedYear.toString(),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                if (showingYears) {
                  final totalYears = lastDate.year - firstDate.year + 1;
                  final maxPageIndex = (totalYears - 1) ~/ 12;
                  if (yearPageIndex < maxPageIndex) {
                    yearPageIndex++;
                  }
                } else {
                  selectedYear = (selectedYear + 1)
                      .clamp(firstDate.year, lastDate.year)
                      .toInt();
                }
              });
            },
            icon: Icon(Icons.chevron_right, color: Colors.black),
          ),
        ],
      ),
    );
  }

  String _getYearPageRangeText() {
    final startYear = firstDate.year + (yearPageIndex * 12);
    final totalYears = lastDate.year - firstDate.year + 1;
    final remainingYears = totalYears - (yearPageIndex * 12);
    final endYear = startYear + (remainingYears > 12 ? 11 : remainingYears - 1);
    return '$startYear - $endYear';
  }

  Widget _buildYearGrid() {
    final startYear = firstDate.year + (yearPageIndex * 12);
    final totalYears = lastDate.year - firstDate.year + 1;
    final remainingYears = totalYears - (yearPageIndex * 12);
    final yearsToShow = remainingYears > 12 ? 12 : remainingYears;

    List<int> years = [];
    for (int i = 0; i < yearsToShow; i++) {
      years.add(startYear + i);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 2.8,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
        ),
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: years.length,
        itemBuilder: (context, index) {
          final year = years[index];

          final isSelected = year == selectedYear;

          return GestureDetector(
            onTap: () {
              setState(() {
                selectedYear = year;

                // Reset selected month if selected year is now the current year and month > today.month
                if (selectedYear == today.year && selectedMonth > today.month) {
                  selectedMonth = today.month;
                }

                showingYears = false;
              });
            },
            child: Container(
              decoration: BoxDecoration(
                color: isSelected ? Colors.blue : Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? Colors.blue : Colors.grey.shade300,
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  year.toString(),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? Colors.white : Colors.black,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMonthGrid() {
    final maxMonth = selectedYear < today.year ? 12 : today.month;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 2.5,
          crossAxisSpacing: 6,
          mainAxisSpacing: 6,
        ),
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: maxMonth,
        itemBuilder: (context, index) {
          final month = index + 1;

          final isSelected = month == selectedMonth;

          return GestureDetector(
            onTap: () {
              setState(() {
                selectedMonth = month;
              });
            },
            child: Container(
              decoration: BoxDecoration(
                color: isSelected ? Colors.blue : Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? Colors.blue : Colors.grey.shade300,
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  DateFormat('MMM').format(DateTime(selectedYear, month)),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? Colors.white : Colors.black,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          TextButton(
            onPressed: widget.onCancel,
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              backgroundColor: Colors.grey.shade200,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Cancel',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          const SizedBox(width: 12),
          TextButton(
            onPressed: () {
              widget.onDateSelected(DateTime(selectedYear, selectedMonth, 1));
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              backgroundColor: Colors.blue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'SET',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}class CommissionCard extends HookWidget {
  const CommissionCard({super.key});

  @override
  Widget build(BuildContext context) {
    final showBrokers = useState<bool>(true);
    final topTenCommissionedBrokers = useState<List<TopPerformers>>([]);
    final topTenCommissionedAgents = useState<List<TopPerformers>>([]);
    final topTenSalesBrokers = useState<List<TopPerformers>>([]);
    final topTenSalesAgents = useState<List<TopPerformers>>([]);
    final user = context.watch<UserCubit>().state.user;
    final userRole = user?.role ?? "";
    final topPerformersCubit = context.read<TopPerformersCubit>();
    final selectMonth = useState<String>(
      DateFormat('yyyy-MM').format(DateTime.now()),
    );

    // Initialize data
    useEffect(() {
      Future.microtask(() async {
        if (userRole == UserRole.agent || userRole == UserRole.brokerage) {
          await topPerformersCubit.getAgentTopPerformers(selectMonth.value);
        } else if (userRole == UserRole.admin ||
            userRole == UserRole.platformOwner) {
          await topPerformersCubit.getBrokerageTopPerformers(selectMonth.value);
          await topPerformersCubit.getAgentTopPerformers(selectMonth.value);
        }
      });
      return null;
    }, [selectMonth.value]);

    final Size size = MediaQuery.of(context).size;
    final isSmallView =
        !Responsive.isMobile(context) &&
        size.width < 1440 &&
        size.width > commissionCardBreakPoint;

    return LayoutBuilder(
      builder: (context, constraints) {
        return BlocConsumer<TopPerformersCubit, TopPerformersState>(
          listener: (context, state) {
            if (state is TopPerformersError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error: ${state.message}')),
              );
            }

            if (state is TopPerformersLoaded) {
              if (state.agentTopPerformers != null) {
                topTenCommissionedAgents.value = [...state.agentTopPerformers!];

                final sortedAgents = [...state.agentTopPerformers!];
                sortedAgents.sort(
                  (a, b) => b.monthlySalesCount.compareTo(a.monthlySalesCount),
                );

                topTenSalesAgents.value = sortedAgents;
              }

              if (state.brokerageTopPerformers != null) {
                topTenCommissionedBrokers.value = [
                  ...state.brokerageTopPerformers!,
                ];

                final sortedBrokers = [...state.brokerageTopPerformers!];
                sortedBrokers.sort(
                  (a, b) => b.monthlySalesCount.compareTo(a.monthlySalesCount),
                );

                topTenSalesBrokers.value = sortedBrokers;
              }
            }
          },
          builder: (context, state) {
            return ClipRRect(
              borderRadius: BorderRadius.circular(15),
              child: Container(
                constraints: BoxConstraints(maxHeight: size.height),
                width: Responsive.isDesktop(context)
                    ? size.width * 0.2
                    : size.width,
                decoration: _buildBoxDecoration(),
                child: Responsive.isTablet(context)
                    ? Container(
                        height: size.height / 1.6,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Expanded(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _buildHeader(
                                    isSmallView,
                                    size,
                                    selectMonth,
                                    topPerformersCubit,
                                    context,
                                  ),
                                  _buildTabRow(size, showBrokers, user),
                                  const SizedBox(height: defaultPadding),
                                  _buildPerformersList(
                                    isSmallView,
                                    user?.role == UserRole.brokerage ||
                                            user?.role == UserRole.officeStaff
                                        ? true
                                        : user?.role == UserRole.agent
                                        ? false
                                        : showBrokers.value,
                                    topTenCommissionedBrokers,
                                    topTenCommissionedAgents,
                                    user,
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              child: ValueListenableBuilder(
                                valueListenable: showBrokers,
                                builder: (context, value, child) {
                                  return SalesByBrokersCard(
                                    brokersList:
                                        topTenCommissionedBrokers.value,
                                    agentsList: topTenCommissionedAgents.value,
                                    isBrokerView: showBrokers.value,
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      )
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildHeader(
                            isSmallView,
                            size,
                            selectMonth,
                            topPerformersCubit,
                            context,
                          ),
                          _buildTabRow(size, showBrokers, user),
                          const SizedBox(height: defaultPadding),
                          _buildPerformersList(
                            isSmallView,
                            user?.role == UserRole.brokerage ||
                                    user?.role == UserRole.officeStaff ||
                                    user?.role == UserRole.agent
                                ? false
                                : showBrokers.value,
                            topTenCommissionedBrokers,
                            topTenCommissionedAgents,
                            user,
                          ),
                          const SizedBox(height: defaultPadding),
                          Flexible(
                            child: SalesByBrokersCard(
                              isBrokerView:
                                  user?.role == UserRole.brokerage ||
                                      user?.role == UserRole.officeStaff ||
                                      user?.role == UserRole.agent
                                  ? false
                                  : showBrokers.value,
                              brokersList: topTenSalesBrokers.value,
                              agentsList: topTenSalesAgents.value,
                            ),
                          ),
                          const SizedBox(height: defaultPadding / 1.5),
                        ],
                      ),
              ),
            );
          },
        );
      },
    );
  }

  BoxDecoration _buildBoxDecoration() {
    return BoxDecoration(
      color: AppTheme.commissionCardColor,
      borderRadius: BorderRadius.circular(15),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.26),
          blurRadius: 5,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  Widget _buildHeader(
    bool isSmallView,
    Size size,
    ValueNotifier<String> selectMonth,
    TopPerformersCubit topPerformersCubit,
    BuildContext context,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: defaultPadding,
        horizontal: defaultPadding,
      ),
      color: AppTheme.commissionCardDarkColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            topPerformers,
            style: AppFonts.semiBoldTextStyle(
              size.width < 1330 && size.width > 1200 ? 16 : 18,
              color: Colors.white,
            ),
          ),
          _buildMonthSelection(size, selectMonth, topPerformersCubit, context),
        ],
      ),
    );
  }

  Widget _buildMonthSelection(
    Size size,
    ValueNotifier<String> selectMonth,
    TopPerformersCubit topPerformersCubit,
    BuildContext context,
  ) {
    bool restrictSize = size.width >= 1200 && size.width < 1330;
    final GlobalKey selectionKey = GlobalKey();
    return GestureDetector(
      key: selectionKey,
      onTap: () {
        _showMonthYearPickerBelow(
          selectionKey.currentContext!,
          selectMonth,
          topPerformersCubit,
          context,
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: restrictSize ? 5 : 12,
          vertical: 5,
        ),
        decoration: BoxDecoration(
          color: AppTheme.commissionDropDownBgColor,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              DateFormat(
                'MMM yyyy',
              ).format(DateTime.parse('${selectMonth.value}-01')),
              style: AppFonts.mediumTextStyle(
                size.width >= 1100 && size.width < 1570 ? 11 : 14,
                color: Colors.white,
              ),
            ),
            SizedBox(width: size.width < 1570 && size.width >= 1100 ? 2 : 4),
            Icon(
              Icons.keyboard_arrow_down,
              color: Colors.white,
              size: restrictSize ? 10 : 16,
            ),
          ],
        ),
      ),
    );
  }

void _showMonthYearPickerBelow(
  BuildContext anchorContext,
  ValueNotifier<String> selectMonth,
  TopPerformersCubit topPerformersCubit,
  BuildContext parentContext,
) {
  final RenderBox renderBox = anchorContext.findRenderObject() as RenderBox;
  final Offset offset = renderBox.localToGlobal(Offset.zero);
  final Size size = renderBox.size;
  final MediaQueryData mediaQuery = MediaQuery.of(parentContext);
  final double screenWidth = mediaQuery.size.width;
  final double screenHeight = mediaQuery.size.height;
  final double padding = 16;
  final DateTime initialDate = DateTime.parse('${selectMonth.value}-01');

  // Calculate picker dimensions based on screen size
  final double pickerWidth = _calculatePickerWidth(screenWidth);
  final double pickerHeight = 350;

  // Calculate optimal position
  final PickerPosition position = _calculateOptimalPosition(
    anchorOffset: offset,
    anchorSize: size,
    screenWidth: screenWidth,
    screenHeight: screenHeight,
    pickerWidth: pickerWidth,
    pickerHeight: pickerHeight,
    padding: padding,
  );

  OverlayEntry? entry;
  entry = OverlayEntry(
    builder: (context) => Stack(
      children: [
        GestureDetector(
          onTap: () {
            entry?.remove();
          },
          child: Container(color: Colors.black.withValues(alpha: 0.3)),
        ),
        Positioned(
          left: position.left,
          top: position.top,
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(8.0),
            child: MonthYearPicker(
              anchorKey: GlobalKey(),
              initialDate: initialDate,
              onDateSelected: (DateTime date) async {
                final formattedDate = DateFormat('yyyy-MM').format(date);
                selectMonth.value = formattedDate;
                final userRole = parentContext
                    .read<UserCubit>()
                    .state
                    .user
                    ?.role;
                if (userRole == UserRole.agent ||
                    userRole == UserRole.brokerage) {
                  await topPerformersCubit.getAgentTopPerformers(
                    formattedDate,
                  );
                } else if (userRole == UserRole.admin ||
                    userRole == UserRole.platformOwner) {
                  await topPerformersCubit.getBrokerageTopPerformers(
                    formattedDate,
                  );
                  await topPerformersCubit.getAgentTopPerformers(
                    formattedDate,
                  );
                }
                entry?.remove();
              },
              onCancel: () {
                entry?.remove();
              },
            ),
          ),
        ),
      ],
    ),
  );

  Overlay.of(parentContext).insert(entry);
}
  Widget _buildTabRow(Size size, ValueNotifier<bool> showBrokers, User? user) {
    return user?.role == UserRole.admin || user?.role == UserRole.platformOwner
        ? Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(
              defaultPadding,
              0,
              defaultPadding,
              defaultPadding,
            ),
            decoration: BoxDecoration(color: AppTheme.commissionCardDarkColor),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(50),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppTheme.commissionCardColor,
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTab(brokerLabel, showBrokers.value, () {
                        showBrokers.value = true;
                      }),
                      _buildTab(agentLabel, !showBrokers.value, () {
                        showBrokers.value = false;
                      }),
                    ],
                  ),
                ),
              ),
            ),
          )
        : SizedBox.shrink();
  }

  Widget _buildTab(String text, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.commissionDropDownBgColor
              : AppTheme.commissionCardColor,
          borderRadius: isSelected
              ? BorderRadius.circular(20)
              : BorderRadius.only(
                  topRight: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
        ),
        child: Text(
          text,
          style: AppFonts.mediumTextStyle(14, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildPerformersList(
    bool isSmallView,
    bool showBrokers,
    ValueNotifier<List<TopPerformers>> topTenBrokers,
    ValueNotifier<List<TopPerformers>> topTenAgents,
    User? user,
  ) {
    return Expanded(
      child: Container(
        color: AppTheme.commissionCardColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              commissionTab,
              textAlign: TextAlign.center,
              style: AppFonts.semiBoldTextStyle(16, color: Colors.white),
            ),
            const SizedBox(height: defaultPadding),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
                itemCount: showBrokers
                    ? topTenBrokers.value.length
                    : topTenAgents.value.length,
                itemBuilder: (context, index) {
                  final bgColor = index % 2 == 0
                      ? AppTheme.commissionCardColor
                      : AppTheme.commissionCardDarkColor;
                  if (showBrokers) {
                    final broker = topTenBrokers.value[index];
                    return _buildBrokerPerformerItem(
                      broker,
                      isSmallView,
                      bgColor,
                    );
                  } else {
                    final agent = topTenAgents.value[index];
                    return _buildAgentPerformerItem(
                      agent,
                      isSmallView,
                      bgColor,
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBrokerPerformerItem(
    TopPerformers broker,
    bool isSmallView,
    Color bgColor,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 5),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 1.3,
          vertical: defaultPadding / 2.5,
        ),
        child: Row(
          children: [
            _userAvatar(),
            const SizedBox(width: 12),
            _nameSalesCountWidget(
              broker.name,
              broker.downlineAgentsCount,
              false,
              '',
            ),
            _earningsWidget(broker.monthlyRevenue),
          ],
        ),
      ),
    );
  }

  Widget _userAvatar() {
    return CircleAvatar(
      radius: 18,
      backgroundColor: Colors.white.withValues(alpha: 0.2),
      child: Icon(Icons.person, color: Colors.white, size: 18),
    );
  }

  Widget _buildAgentPerformerItem(
    TopPerformers agent,
    bool isSmallView,
    Color bgColor,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 5),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 1.3,
          vertical: defaultPadding / 2.5,
        ),
        child: Row(
          children: [
            _userAvatar(),
            const SizedBox(width: 12),
            _nameSalesCountWidget(
              agent.name,
              agent.downlineAgentsCount,
              true,
              agent.associatedBroker,
            ),
            SizedBox(width: 12),
            _earningsWidget(agent.monthlyRevenue),
          ],
        ),
      ),
    );
  }

  Widget _nameSalesCountWidget(
    String name,
    int agentsCount,
    bool isAgent,
    String relatedBroker,
  ) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.semiBoldTextStyle(13, color: Colors.white),
          ),
          SizedBox(height: 3),
          Text(
            '$agentsCount $agentLabel',
            style: AppFonts.regularTextStyle(
              11,
              color: AppTheme.commissionSalesTextColor,
            ),
          ),

          if (isAgent) ...[
            Divider(color: AppTheme.commissionSalesTextColor),
            Text(
              relatedBrokerLabel,
              style: AppFonts.regularTextStyle(
                11,
                color: AppTheme.commissionSalesTextColor,
              ),
            ),
            SizedBox(height: 3),
            Text(
              relatedBroker,
              style: AppFonts.semiBoldTextStyle(13, color: AppTheme.white),
            ),
          ],
        ],
      ),
    );
  }

  Widget _earningsWidget(double totalCommission) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '\$${totalCommission.toInt()}',
          style: AppFonts.semiBoldTextStyle(14, color: Colors.white),
        ),
        SizedBox(height: 3),
        Text(
          revenueEarnedLabel,
          maxLines: 2,
          style: AppFonts.mediumTextStyle(
            10,
            color: AppTheme.commissionSalesTextColor,
          ),
        ),
      ],
    );
  }
}

double _calculatePickerWidth(double screenWidth) {
  if (screenWidth <= 480) {
    return screenWidth - 32;
  } else if (screenWidth <= 768) {
    // Tablet: Fixed width but responsive
    return 320;
  } else {
    // Desktop: Standard width
    return 320;
  }
}

// Calculate optimal position for the picker
PickerPosition _calculateOptimalPosition({
  required Offset anchorOffset,
  required Size anchorSize,
  required double screenWidth,
  required double screenHeight,
  required double pickerWidth,
  required double pickerHeight,
  required double padding,
}) {
  double left = anchorOffset.dx;
  double top = anchorOffset.dy + anchorSize.height + 8;

  // Adjust horizontal position
  // First, try to align the picker's right edge with the anchor's right edge
  left = anchorOffset.dx + anchorSize.width - pickerWidth;

  // If picker goes off the left edge, align left edges
  if (left < padding) {
    left = anchorOffset.dx;
  }

  // If picker still goes off the right edge, center it or move it left
  if (left + pickerWidth > screenWidth - padding) {
    if (pickerWidth <= screenWidth - (2 * padding)) {
      // Center the picker if it fits
      left = (screenWidth - pickerWidth) / 2;
    } else {
      // If picker is too wide, align to left with padding
      left = padding;
    }
  }

  // Ensure left position is within bounds
  left = left.clamp(padding, screenWidth - pickerWidth - padding);

  // Adjust vertical position if picker goes off bottom of screen
  if (top + pickerHeight > screenHeight - padding) {
    // Try to position above the anchor
    final double topPosition = anchorOffset.dy - pickerHeight - 8;
    if (topPosition >= padding) {
      top = topPosition;
    } else {
      // If it doesn't fit above either, position it as high as possible
      top = padding;
    }
  }

  return PickerPosition(left: left, top: top);
}